#!/usr/bin/env python3
"""
Test script to verify the refactored markerpdf module works correctly
"""

def test_core_imports():
    """Test that core modules can be imported"""
    try:
        from markerpdf.core import OCRProcessor, save_model_output, save_results_to_markdown
        print("✅ Core module imports successful")
        return True
    except Exception as e:
        print(f"❌ Core module import failed: {e}")
        return False

def test_config_imports():
    """Test that config modules can be imported"""
    try:
        from markerpdf.config import create_gemini_config, create_openrouter_config
        print("✅ Config module imports successful")
        return True
    except Exception as e:
        print(f"❌ Config module import failed: {e}")
        return False

def test_proxy_types_imports():
    """Test that proxy types can be imported"""
    try:
        from markerpdf.proxy_types import HTTPProxyHandler, SOCKS5ProxyHandler, ProxyType
        print("✅ Proxy types module imports successful")
        return True
    except Exception as e:
        print(f"❌ Proxy types module import failed: {e}")
        return False

def test_http_proxy_imports():
    """Test that HTTP proxy module can be imported"""
    try:
        from markerpdf.marker_ocr_http_proxy import (
            create_gemini_config_with_http_proxy,
            test_model_http_proxy,
            test_model_with_http_proxy_retry
        )
        print("✅ HTTP proxy module imports successful")
        return True
    except Exception as e:
        print(f"❌ HTTP proxy module import failed: {e}")
        return False

def test_socks5_proxy_imports():
    """Test that SOCKS5 proxy module can be imported"""
    try:
        from markerpdf.marker_ocr_socks5_proxy import (
            create_gemini_config,
            create_openrouter_config,
            test_model,
            test_model_with_retry
        )
        print("✅ SOCKS5 proxy module imports successful")
        return True
    except Exception as e:
        print(f"❌ SOCKS5 proxy module import failed: {e}")
        return False

def test_main_package_imports():
    """Test that main package imports work"""
    try:
        from markerpdf import (
            create_gemini_config_with_http_proxy,
            test_model,
            save_results_to_markdown
        )
        print("✅ Main package imports successful")
        return True
    except Exception as e:
        print(f"❌ Main package import failed: {e}")
        return False

def test_comparison_tool_imports():
    """Test that comparison tool can be imported"""
    try:
        from markerpdf.compare_proxy_types import (
            test_socks5_proxy_with_timing,
            test_http_proxy_with_timing,
            save_comparison_results
        )
        print("✅ Comparison tool imports successful")
        return True
    except Exception as e:
        print(f"❌ Comparison tool import failed: {e}")
        return False

def main():
    """Run all tests"""
    print("🧪 Testing refactored markerpdf module...")
    print("=" * 50)
    
    tests = [
        test_core_imports,
        test_config_imports,
        test_proxy_types_imports,
        test_http_proxy_imports,
        test_socks5_proxy_imports,
        test_main_package_imports,
        test_comparison_tool_imports
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
        print()
    
    print("=" * 50)
    print(f"📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Refactoring successful.")
        return True
    else:
        print("⚠️ Some tests failed. Please check the issues above.")
        return False

if __name__ == "__main__":
    main()
