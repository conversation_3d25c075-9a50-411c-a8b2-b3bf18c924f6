"""
Proxy type handlers for different proxy implementations
Handles HTTP and SOCKS5 proxy setup and management
"""

import os
from abc import ABC, abstractmethod
from typing import Dict, Any, Optional
from enum import Enum

from .proxy_manager import ProxyManager


class ProxyType(Enum):
    """Enumeration of supported proxy types"""
    HTTP = "http"
    SOCKS5 = "socks5"


class BaseProxyHandler(ABC):
    """Base class for proxy handlers"""
    
    def __init__(self, proxy_manager: Optional[ProxyManager] = None):
        """
        Initialize proxy handler
        
        Args:
            proxy_manager: ProxyManager instance, creates new one if None
        """
        self.proxy_manager = proxy_manager or ProxyManager()
        self.proxy_type = None
    
    @abstractmethod
    def setup_proxy(self, force: bool = False) -> bool:
        """
        Set up the proxy connection
        
        Args:
            force: Force proxy setup even if already configured
            
        Returns:
            True if proxy setup successful, False otherwise
        """
        pass
    
    @abstractmethod
    def test_connection(self, timeout: int = 30) -> bool:
        """
        Test proxy connection
        
        Args:
            timeout: Connection timeout in seconds
            
        Returns:
            True if connection successful, False otherwise
        """
        pass
    
    def get_proxy_info(self) -> Dict[str, Any]:
        """Get information about current proxy setup"""
        return {
            'proxy_type': self.proxy_type.value if self.proxy_type else None,
            'proxy_manager': self.proxy_manager is not None
        }


class HTTPProxyHandler(BaseProxyHandler):
    """Handler for HTTP proxy connections (converted from SOCKS5)"""
    
    def __init__(self, proxy_manager: Optional[ProxyManager] = None):
        """Initialize HTTP proxy handler"""
        super().__init__(proxy_manager)
        self.proxy_type = ProxyType.HTTP
    
    def setup_proxy(self, force: bool = False) -> bool:
        """
        Set up HTTP proxy (converts SOCKS5 to HTTP using pproxy)
        
        Args:
            force: Force proxy setup even if already configured
            
        Returns:
            True if proxy setup successful, False otherwise
        """
        print("🔧 Setting up HTTP proxy (converting from SOCKS5)...")
        
        nordvpn_proxy = os.getenv('NORDVPN_PROXY')
        if not nordvpn_proxy:
            print("⚠️ No NORDVPN_PROXY environment variable found")
            return False
        
        try:
            # Use ProxyManager to set up HTTP proxy with SOCKS5 conversion
            self.proxy_manager.set_global_proxy_with_conversion(nordvpn_proxy, force=force)
            print("✅ HTTP proxy setup completed")
            return True
            
        except Exception as e:
            print(f"❌ HTTP proxy setup failed: {e}")
            return False
    
    def test_connection(self, timeout: int = 30) -> bool:
        """
        Test HTTP proxy connection
        
        Args:
            timeout: Connection timeout in seconds
            
        Returns:
            True if connection successful, False otherwise
        """
        print(f"🧪 Testing HTTP proxy connection (timeout: {timeout}s)...")
        
        try:
            success = self.proxy_manager.test_proxy_connection(timeout=timeout)
            if success:
                print("✅ HTTP proxy connection successful")
            else:
                print("⚠️ HTTP proxy connection test failed")
            return success
            
        except Exception as e:
            print(f"❌ HTTP proxy connection test error: {e}")
            return False
    
    def get_proxy_info(self) -> Dict[str, Any]:
        """Get HTTP proxy information"""
        info = super().get_proxy_info()
        info.update({
            'conversion_method': 'SOCKS5 to HTTP via pproxy',
            'local_port': getattr(self.proxy_manager, 'http_proxy_port', 8888)
        })
        return info


class SOCKS5ProxyHandler(BaseProxyHandler):
    """Handler for direct SOCKS5 proxy connections"""
    
    def __init__(self, proxy_manager: Optional[ProxyManager] = None):
        """Initialize SOCKS5 proxy handler"""
        super().__init__(proxy_manager)
        self.proxy_type = ProxyType.SOCKS5
    
    def setup_proxy(self, force: bool = False) -> bool:
        """
        Set up SOCKS5 proxy
        
        Args:
            force: Force proxy setup even if already configured
            
        Returns:
            True if proxy setup successful, False otherwise
        """
        print("🔧 Setting up SOCKS5 proxy...")
        
        nordvpn_proxy = os.getenv('NORDVPN_PROXY')
        if not nordvpn_proxy:
            print("⚠️ No NORDVPN_PROXY environment variable found")
            return False
        
        try:
            # Use ProxyManager to set up direct SOCKS5 proxy
            self.proxy_manager.set_global_proxy(nordvpn_proxy, force=force)
            print("✅ SOCKS5 proxy setup completed")
            return True
            
        except Exception as e:
            print(f"❌ SOCKS5 proxy setup failed: {e}")
            return False
    
    def test_connection(self, timeout: int = 30) -> bool:
        """
        Test SOCKS5 proxy connection
        
        Args:
            timeout: Connection timeout in seconds
            
        Returns:
            True if connection successful, False otherwise
        """
        print(f"🧪 Testing SOCKS5 proxy connection (timeout: {timeout}s)...")
        
        try:
            success = self.proxy_manager.test_proxy_connection(timeout=timeout)
            if success:
                print("✅ SOCKS5 proxy connection successful")
            else:
                print("⚠️ SOCKS5 proxy connection test failed")
            return success
            
        except Exception as e:
            print(f"❌ SOCKS5 proxy connection test error: {e}")
            return False
    
    def get_proxy_info(self) -> Dict[str, Any]:
        """Get SOCKS5 proxy information"""
        info = super().get_proxy_info()
        info.update({
            'connection_method': 'Direct SOCKS5'
        })
        return info


def create_proxy_handler(proxy_type: ProxyType, 
                        proxy_manager: Optional[ProxyManager] = None) -> BaseProxyHandler:
    """
    Factory function to create appropriate proxy handler
    
    Args:
        proxy_type: Type of proxy to create
        proxy_manager: Optional ProxyManager instance
        
    Returns:
        Appropriate proxy handler instance
    """
    if proxy_type == ProxyType.HTTP:
        return HTTPProxyHandler(proxy_manager)
    elif proxy_type == ProxyType.SOCKS5:
        return SOCKS5ProxyHandler(proxy_manager)
    else:
        raise ValueError(f"Unsupported proxy type: {proxy_type}")


def get_default_proxy_handler(proxy_manager: Optional[ProxyManager] = None) -> HTTPProxyHandler:
    """
    Get the default proxy handler (HTTP proxy as per requirements)
    
    Args:
        proxy_manager: Optional ProxyManager instance
        
    Returns:
        HTTP proxy handler instance
    """
    return HTTPProxyHandler(proxy_manager)


def setup_proxy_for_model(proxy_type: ProxyType, model_name: str, 
                         proxy_manager: Optional[ProxyManager] = None) -> BaseProxyHandler:
    """
    Set up proxy for a specific model
    
    Args:
        proxy_type: Type of proxy to set up
        model_name: Name of the model (for logging)
        proxy_manager: Optional ProxyManager instance
        
    Returns:
        Configured proxy handler
    """
    print(f"\n🔧 Setting up {proxy_type.value.upper()} proxy for {model_name}")
    
    handler = create_proxy_handler(proxy_type, proxy_manager)
    
    # Set up the proxy
    setup_success = handler.setup_proxy(force=True)
    
    if setup_success:
        # Test the connection
        test_success = handler.test_connection()
        if not test_success:
            print(f"⚠️ Proxy connection test failed for {model_name}, but continuing")
    else:
        print(f"❌ Proxy setup failed for {model_name}")
    
    return handler


# Convenience functions for backward compatibility
def setup_http_proxy(proxy_manager: Optional[ProxyManager] = None) -> HTTPProxyHandler:
    """Set up HTTP proxy handler"""
    return HTTPProxyHandler(proxy_manager)


def setup_socks5_proxy(proxy_manager: Optional[ProxyManager] = None) -> SOCKS5ProxyHandler:
    """Set up SOCKS5 proxy handler"""
    return SOCKS5ProxyHandler(proxy_manager)
