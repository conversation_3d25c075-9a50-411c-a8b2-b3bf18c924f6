import os
import time
from datetime import datetime
from pathlib import Path

# Early proxy setup before any other imports
from dotenv import load_dotenv
load_dotenv()

# Early proxy setup - must be executed before other library imports
nordvpn_proxy = os.getenv('NORDVPN_PROXY')
if nordvpn_proxy:
    from .proxy_manager import setup_early_proxy
    setup_early_proxy(nordvpn_proxy)

# Import new core modules
from .core import OCRProcessor, save_results_to_markdown
from .config import create_gemini_config as create_gemini_config_core, create_openrouter_config
from .proxy_types import SOCKS5ProxyHandler, ProxyType
from .proxy_manager import ProxyManager

# Create global proxy manager and SOCKS5 proxy handler
proxy_manager = ProxyManager()
socks5_proxy_handler = SOCKS5ProxyHandler(proxy_manager)

def create_gemini_config():
    """Create Gemini model configuration"""
    print(f"\n{'='*50}")
    print("Configuring Gemini model (requires proxy)")
    print(f"{'='*50}")

    # Set up SOCKS5 proxy
    socks5_proxy_handler.setup_proxy(force=True)
    socks5_proxy_handler.test_connection()

    # Create Gemini configuration
    config = create_gemini_config_core(proxy_timeout=30)
    config['proxy_type'] = 'SOCKS5'

    return config

def create_openrouter_config():
    """Create OpenRouter Deepseek model configuration"""
    print(f"\n{'='*50}")
    print("Configuring OpenRouter model (clearing proxy)")
    print(f"{'='*50}")

    # Clear proxy settings (OpenRouter doesn't need proxy)
    proxy_manager.clear_proxy()

    # Use the new config module
    return create_openrouter_config()

def save_model_output(text, images, model_name, config, timestamp, base_dir="ocr_comparison_results"):
    """Save model output to separate folder, including markdown files and images"""
    # Create model-specific folder
    model_folder = Path(base_dir) / f"{model_name.replace(' ', '_').replace('/', '_')}_{timestamp}"
    model_folder.mkdir(parents=True, exist_ok=True)

    # Save markdown file
    markdown_file = model_folder / "ocr_result.md"
    with open(markdown_file, 'w', encoding='utf-8') as f:
        f.write(f"# {model_name} OCR Results\n\n")
        f.write(f"Generated at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")

        # Add configuration information
        f.write("## Configuration Parameters\n\n")
        f.write(f"- **Batch Multiplier**: {config.get('batch_multiplier', 1)}\n")
        f.write(f"- **Languages**: {', '.join(config.get('langs', ['en']))}\n")
        f.write(f"- **Use LLM**: {config.get('use_llm', True)}\n")
        f.write(f"- **Force OCR**: {config.get('force_ocr', False)}\n")
        f.write(f"- **Format Lines**: {config.get('format_lines', True)}\n\n")

        f.write("## Extracted Text Content\n\n")
        f.write(text)

    # Save image files
    saved_images = []
    for i, (image_name, image_data) in enumerate(images.items()):
        # Use original image name if available, otherwise use index
        if image_name:
            image_filename = f"{image_name}"
        else:
            image_filename = f"image_{i+1}.png"

        image_path = model_folder / image_filename

        # Save image
        try:
            image_data.save(image_path)
            saved_images.append(image_filename)
            print(f"  Saved image: {image_filename}")
        except Exception as e:
            print(f"  Failed to save image {image_filename}: {e}")

    # Create image list file
    if saved_images:
        images_list_file = model_folder / "images_list.md"
        with open(images_list_file, 'w', encoding='utf-8') as f:
            f.write(f"# {model_name} Extracted Images List\n\n")
            f.write(f"Total extracted images: {len(saved_images)}\n\n")
            for img_name in saved_images:
                f.write(f"- ![{img_name}]({img_name})\n")

    print(f"  Model output saved to: {model_folder}")
    return model_folder

def test_model_with_retry(config, model_name, pdf_path="sample.pdf", max_retries=3):
    """Test specified model's OCR performance with retry mechanism"""
    print(f"\n{'='*50}")
    print(f"Starting test for {model_name}")
    print(f"{'='*50}")

    # Create OCR processor with proxy manager
    ocr_processor = OCRProcessor(proxy_manager)

    # Define proxy setup function for retries
    def proxy_setup_func():
        if "Gemini" in model_name:
            socks5_proxy_handler.setup_proxy(force=True)

    # Use the core processor with retry mechanism
    result = ocr_processor.process_with_retry(
        config=config,
        model_name=model_name,
        pdf_path=pdf_path,
        max_retries=max_retries,
        proxy_setup_func=proxy_setup_func
    )

    # Add SOCKS5 proxy specific metadata
    result['proxy_type'] = 'SOCKS5'

    return result

def test_model(config, model_name, pdf_path="sample.pdf"):
    """Test specified model's OCR performance (maintain original interface compatibility)"""
    return test_model_with_retry(config, model_name, pdf_path)

# save_results_to_markdown is now imported from core module


if __name__ == "__main__":
    print("=== SOCKS5 Proxy OCR Test ===")
    print("Testing Gemini API with direct SOCKS5 proxy")
    print("="*50)

    # Test Gemini with SOCKS5 proxy
    gemini_config = create_gemini_config()
    gemini_result = test_model(gemini_config, "Gemini 2.5 Flash (SOCKS5 Proxy)")

    if gemini_result['success']:
        print(f"\n✅ Gemini SOCKS5 proxy test successful!")
        print(f"Processing time: {gemini_result['processing_time']:.2f}s")
        print(f"Output folder: {gemini_result['output_folder']}")
    else:
        print(f"\n❌ Gemini SOCKS5 proxy test failed: {gemini_result['error']}")

    print("\n=== SOCKS5 Proxy Test Complete ===")
