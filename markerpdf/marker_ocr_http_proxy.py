import os
import time
from datetime import datetime
from pathlib import Path

# Early proxy setup before any other imports
from dotenv import load_dotenv
load_dotenv()

# Early proxy setup - must be executed before other library imports
nordvpn_proxy = os.getenv('NORDVPN_PROXY')
if nordvpn_proxy:
    from .proxy_manager import setup_early_proxy
    # Note: Still using original SOCKS5 proxy for early setup
    # Will be converted to HTTP proxy later through ProxyManager
    setup_early_proxy(nordvpn_proxy)

# Import new core modules
from .core import OCRProcessor, save_results_to_markdown
from .config import create_gemini_config
from .proxy_types import HTT<PERSON>roxyHandler, ProxyType
from .proxy_manager import ProxyManager

# Create global proxy manager and HTTP proxy handler
proxy_manager = ProxyManager()
http_proxy_handler = HTTPProxyHandler(proxy_manager)

def create_gemini_config_with_http_proxy():
    """Create Gemini model configuration using HTTP proxy converted from SOCKS5"""
    print(f"\n{'='*50}")
    print("Configuring Gemini model with HTTP proxy (converted from SOCKS5)")
    print(f"{'='*50}")

    # Set up HTTP proxy
    http_proxy_handler.setup_proxy(force=True)
    http_proxy_handler.test_connection()

    # Create Gemini configuration with HTTP proxy timeout
    config = create_gemini_config(proxy_timeout=30)
    config['proxy_type'] = 'HTTP (converted from SOCKS5)'

    return config

def create_openrouter_config():
    """Create OpenRouter Deepseek model configuration"""
    print(f"\n{'='*50}")
    print("Configuring OpenRouter model (clearing proxy)")
    print(f"{'='*50}")

    # Clear proxy settings (OpenRouter doesn't need proxy)
    proxy_manager.clear_proxy()

    # Use the new config module
    from .config import create_openrouter_config as create_openrouter_config_core
    return create_openrouter_config_core()

def save_model_output(text, images, model_name, config, timestamp, base_dir="ocr_http_proxy_results"):
    """Save model output to separate folder, including markdown files and images"""
    # Create model-specific folder
    model_folder = Path(base_dir) / f"{model_name.replace(' ', '_').replace('/', '_')}_{timestamp}"
    model_folder.mkdir(parents=True, exist_ok=True)

    # Save markdown file
    markdown_file = model_folder / "ocr_result.md"
    with open(markdown_file, 'w', encoding='utf-8') as f:
        f.write(f"# {model_name} OCR Results (HTTP Proxy Test)\n\n")
        f.write(f"Generated at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
        f.write("**Proxy Type**: HTTP (converted from SOCKS5 using pproxy)\n\n")

        # Add configuration information
        f.write("## Configuration Parameters\n\n")
        f.write(f"- **Batch Multiplier**: {config.get('batch_multiplier', 1)}\n")
        f.write(f"- **Languages**: {', '.join(config.get('langs', ['en']))}\n")
        f.write(f"- **Use LLM**: {config.get('use_llm', True)}\n")
        f.write(f"- **Force OCR**: {config.get('force_ocr', False)}\n")
        f.write(f"- **Format Lines**: {config.get('format_lines', True)}\n\n")

        f.write("## Extracted Text Content\n\n")
        f.write(text)

    # Save image files
    saved_images = []
    for i, (image_name, image_data) in enumerate(images.items()):
        # Use original image name if available, otherwise use index
        if image_name:
            image_filename = f"{image_name}"
        else:
            image_filename = f"image_{i+1}.png"

        image_path = model_folder / image_filename

        # Save image
        try:
            image_data.save(image_path)
            saved_images.append(image_filename)
            print(f"  Saved image: {image_filename}")
        except Exception as e:
            print(f"  Failed to save image {image_filename}: {e}")

    # Create image list file
    if saved_images:
        images_list_file = model_folder / "images_list.md"
        with open(images_list_file, 'w', encoding='utf-8') as f:
            f.write(f"# {model_name} Extracted Images List (HTTP Proxy Test)\n\n")
            f.write(f"Total extracted images: {len(saved_images)}\n\n")
            for img_name in saved_images:
                f.write(f"- ![{img_name}]({img_name})\n")

    print(f"  Model output saved to: {model_folder}")
    return model_folder

def test_model_with_http_proxy_retry(config, model_name, pdf_path="sample.pdf", max_retries=None):
    """Test specified model's OCR performance with HTTP proxy and retry mechanism"""
    # Use retry count from config if not specified, otherwise use config default
    if max_retries is None:
        max_retries = config.get('max_retries', 3)

    print(f"\n{'='*50}")
    print(f"Starting HTTP proxy test for {model_name}")
    print(f"Max Retries: {max_retries}")
    print(f"{'='*50}")

    # Create OCR processor with proxy manager
    ocr_processor = OCRProcessor(proxy_manager)

    # Define proxy setup function for retries
    def proxy_setup_func():
        if "Gemini" in model_name:
            http_proxy_handler.setup_proxy(force=True)

    # Use the core processor with retry mechanism
    result = ocr_processor.process_with_retry(
        config=config,
        model_name=model_name,
        pdf_path=pdf_path,
        max_retries=max_retries,
        proxy_setup_func=proxy_setup_func
    )

    # Add HTTP proxy specific metadata
    result['proxy_type'] = 'HTTP (converted from SOCKS5)'

    return result


def test_model_http_proxy(config, model_name, pdf_path="sample.pdf"):
    """Test specified model's OCR performance with HTTP proxy (maintain interface compatibility)"""
    return test_model_with_http_proxy_retry(config, model_name, pdf_path)


if __name__ == "__main__":
    print("=== HTTP Proxy OCR Test ===")
    print("This test uses pproxy to convert SOCKS5 proxy to HTTP proxy")
    print("Testing Gemini API compatibility with HTTP proxy vs SOCKS5 proxy")
    print("="*50)

    # Test Gemini with HTTP proxy
    gemini_config = create_gemini_config_with_http_proxy()
    gemini_result = test_model_http_proxy(gemini_config, "Gemini 2.5 Flash (HTTP Proxy)")

    if gemini_result['success']:
        print(f"\n✅ Gemini HTTP proxy test successful!")
        print(f"Processing time: {gemini_result['processing_time']:.2f}s")
        print(f"Output folder: {gemini_result['output_folder']}")
    else:
        print(f"\n❌ Gemini HTTP proxy test failed: {gemini_result['error']}")

    # Clean up proxy converter
    proxy_manager.clear_proxy()
    print("\n=== HTTP Proxy Test Complete ===")
