"""
Core OCR processing functionality for marker-pdf
Extracted common functionality to eliminate code duplication between proxy implementations
"""

import os
import time
from datetime import datetime
from pathlib import Path
from typing import Dict, Any, Optional, List

from marker.converters.pdf import PdfConverter
from marker.models import create_model_dict
from marker.config.parser import ConfigParser
from marker.output import text_from_rendered


class OCRProcessor:
    """Core OCR processor that handles marker-pdf processing logic"""
    
    def __init__(self, proxy_manager=None):
        """
        Initialize OCR processor
        
        Args:
            proxy_manager: ProxyManager instance for handling proxy settings
        """
        self.proxy_manager = proxy_manager
    
    def process_pdf(self, config: Dict[str, Any], pdf_path: str = "sample.pdf") -> Dict[str, Any]:
        """
        Process PDF using marker-pdf with given configuration
        
        Args:
            config: Configuration dictionary for the OCR model
            pdf_path: Path to the PDF file to process
            
        Returns:
            Dictionary containing processing results
        """
        print(f"🔄 Processing PDF: {pdf_path}")
        
        # Check if PDF file exists
        if not os.path.exists(pdf_path):
            raise FileNotFoundError(f"PDF file not found: {pdf_path}")
        
        # Create configuration parser and converter
        config_parser = ConfigParser(config)
        converter = PdfConverter(
            config=config_parser.generate_config_dict(),
            artifact_dict=create_model_dict(),
            processor_list=config_parser.get_processors(),
            renderer=config_parser.get_renderer(),
            llm_service=config_parser.get_llm_service()
        )
        
        # Process the PDF
        start_time = time.time()
        rendered = converter(pdf_path)
        processing_time = time.time() - start_time
        
        # Extract text and images
        text, images = text_from_rendered(rendered)
        
        print(f"✅ PDF processing completed in {processing_time:.2f}s")
        print(f"📄 Extracted text length: {len(text)} characters")
        print(f"🖼️ Extracted images: {len(images)} images")
        
        return {
            'text': text,
            'images': images,
            'processing_time': processing_time,
            'rendered': rendered
        }
    
    def process_with_retry(self, config: Dict[str, Any], model_name: str, 
                          pdf_path: str = "sample.pdf", max_retries: int = 3,
                          proxy_setup_func: Optional[callable] = None) -> Dict[str, Any]:
        """
        Process PDF with retry mechanism
        
        Args:
            config: Configuration dictionary
            model_name: Name of the model being tested
            pdf_path: Path to PDF file
            max_retries: Maximum number of retry attempts
            proxy_setup_func: Optional function to call for proxy setup on retries
            
        Returns:
            Dictionary containing processing results and metadata
        """
        error_message = "Unknown error occurred"
        
        for attempt in range(max_retries):
            try:
                if attempt > 0:
                    print(f"\nAttempt {attempt + 1}...")
                    
                    # Call proxy setup function if provided
                    if proxy_setup_func:
                        proxy_setup_func()
                
                # Process the PDF
                result = self.process_pdf(config, pdf_path)
                
                # Save results
                timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
                output_folder = save_model_output(
                    result['text'], 
                    result['images'], 
                    model_name, 
                    config, 
                    timestamp
                )
                
                return {
                    'model_name': model_name,
                    'processing_time': result['processing_time'],
                    'text_length': len(result['text']),
                    'image_count': len(result['images']),
                    'text_content': result['text'],
                    'images': result['images'],
                    'output_folder': output_folder,
                    'config': config,
                    'success': True,
                    'error': None,
                    'attempts': attempt + 1
                }
                
            except Exception as e:
                error_message = str(e)
                print(f"❌ Attempt {attempt + 1} failed: {error_message}")
                
                # Check if this is a retryable error
                if "rate limit" in error_message.lower() or "timeout" in error_message.lower():
                    if attempt < max_retries - 1:
                        wait_time = (attempt + 1) * 5  # Progressive backoff
                        print(f"⏳ Waiting {wait_time}s before retry...")
                        time.sleep(wait_time)
                        continue
                
                # For non-retryable errors, break immediately
                if attempt == 0 and "authentication" in error_message.lower():
                    print("💥 Authentication error detected, stopping retries")
                    break
        
        # All retries failed
        print(f"\n❌ All {max_retries} attempts failed")
        return {
            'model_name': model_name,
            'processing_time': 0,
            'text_length': 0,
            'image_count': 0,
            'text_content': '',
            'images': {},
            'output_folder': None,
            'config': config,
            'success': False,
            'error': error_message,
            'attempts': max_retries
        }


def save_model_output(text: str, images: Dict, model_name: str, 
                     config: Dict[str, Any], timestamp: str) -> str:
    """
    Save model output to files
    
    Args:
        text: Extracted text content
        images: Dictionary of extracted images
        model_name: Name of the model
        config: Configuration used
        timestamp: Timestamp string for file naming
        
    Returns:
        Path to the output folder
    """
    # Create output folder
    safe_model_name = model_name.replace(" ", "_").replace("(", "").replace(")", "")
    output_folder = f"output_{safe_model_name}_{timestamp}"
    Path(output_folder).mkdir(exist_ok=True)
    
    # Save text content
    text_file = Path(output_folder) / "extracted_text.md"
    with open(text_file, 'w', encoding='utf-8') as f:
        f.write(f"# OCR Results - {model_name}\n\n")
        f.write(f"**Processing Time:** {timestamp}\n\n")
        f.write(f"**Configuration:**\n")
        for key, value in config.items():
            f.write(f"- {key}: {value}\n")
        f.write(f"\n**Extracted Text:**\n\n{text}")
    
    # Save images
    if images:
        images_folder = Path(output_folder) / "images"
        images_folder.mkdir(exist_ok=True)
        
        for page_num, page_images in images.items():
            if isinstance(page_images, list):
                for i, img in enumerate(page_images):
                    img_path = images_folder / f"page_{page_num}_image_{i}.png"
                    img.save(img_path)
    
    print(f"💾 Results saved to: {output_folder}")
    return output_folder


def save_results_to_markdown(results: List[Dict[str, Any]], 
                           output_file: str = "ocr_comparison_results.md") -> str:
    """
    Save comparison results to a markdown file
    
    Args:
        results: List of result dictionaries from different models
        output_file: Output markdown file name
        
    Returns:
        Path to the saved markdown file
    """
    timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write(f"# OCR Model Comparison Results\n\n")
        f.write(f"**Generated:** {timestamp}\n\n")
        
        # Summary table
        f.write("## Summary\n\n")
        f.write("| Model | Success | Processing Time | Text Length | Images | Output Folder |\n")
        f.write("|-------|---------|----------------|-------------|--------|--------------|\n")
        
        for result in results:
            success_icon = "✅" if result.get('success', False) else "❌"
            processing_time = f"{result.get('processing_time', 0):.2f}s"
            text_length = result.get('text_length', 0)
            image_count = result.get('image_count', 0)
            output_folder = result.get('output_folder', 'N/A')
            
            f.write(f"| {result.get('model_name', 'Unknown')} | {success_icon} | "
                   f"{processing_time} | {text_length} | {image_count} | {output_folder} |\n")
        
        # Detailed results
        f.write("\n## Detailed Results\n\n")
        
        for result in results:
            f.write(f"### {result.get('model_name', 'Unknown Model')}\n\n")
            
            if result.get('success', False):
                f.write(f"- **Status:** ✅ Success\n")
                f.write(f"- **Processing Time:** {result.get('processing_time', 0):.2f} seconds\n")
                f.write(f"- **Text Length:** {result.get('text_length', 0)} characters\n")
                f.write(f"- **Images Extracted:** {result.get('image_count', 0)}\n")
                f.write(f"- **Output Folder:** {result.get('output_folder', 'N/A')}\n")
                f.write(f"- **Attempts:** {result.get('attempts', 1)}\n")
            else:
                f.write(f"- **Status:** ❌ Failed\n")
                f.write(f"- **Error:** {result.get('error', 'Unknown error')}\n")
                f.write(f"- **Attempts:** {result.get('attempts', 1)}\n")
            
            f.write("\n")
    
    print(f"📊 Comparison results saved to: {output_file}")
    return output_file
