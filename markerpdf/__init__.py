"""
markerpdf package for marker-pdf OCR processing with proxy support

This package provides OCR functionality with HTTP proxy as the default,
while maintaining support for SOCKS5 proxy and comparison tools.
"""

# Core modules (new refactored structure)
from .core import save_model_output, save_results_to_markdown
from .config import create_gemini_config, create_openrouter_config

# Default HTTP proxy functions (recommended)
from .marker_ocr_http_proxy import (
    create_gemini_config_with_http_proxy,
    test_model_http_proxy,
    test_model_with_http_proxy_retry,
    create_openrouter_config
)

# SOCKS5 proxy functions (for comparison and legacy support)
from .marker_ocr_socks5_proxy import (
    create_gemini_config as create_gemini_config_socks5,
    create_openrouter_config as create_openrouter_config_socks5,
    test_model as test_model_socks5,
    test_model_with_retry as test_model_with_retry_socks5
)

# Comparison and testing tools
from .compare_proxy_types import (
    test_socks5_proxy_with_timing,
    test_http_proxy_with_timing,
    save_comparison_results,
    main as compare_main
)

# Proxy management
from .proxy_manager import (
    ProxyManager,
    setup_early_proxy,
    ensure_proxy_persistence
)

# Default exports (HTTP proxy as default per requirements)
# These are the recommended functions for new code
test_model = test_model_http_proxy
test_model_with_retry = test_model_with_http_proxy_retry

__version__ = "1.0.0"
__author__ = "Brian"

__all__ = [
    # Core modules
    "save_model_output",
    "save_results_to_markdown",
    "create_gemini_config",
    "create_openrouter_config",

    # Default functions (HTTP proxy)
    "test_model",  # Points to HTTP proxy version
    "test_model_with_retry",  # Points to HTTP proxy version
    "create_gemini_config_with_http_proxy",
    "test_model_http_proxy",
    "test_model_with_http_proxy_retry",

    # SOCKS5 proxy functions (for comparison)
    "create_gemini_config_socks5",
    "create_openrouter_config_socks5",
    "test_model_socks5",
    "test_model_with_retry_socks5",

    # Comparison functions
    "test_socks5_proxy_with_timing",
    "test_http_proxy_with_timing",
    "save_comparison_results",
    "compare_main",

    # Proxy management
    "ProxyManager",
    "setup_early_proxy",
    "ensure_proxy_persistence"
]